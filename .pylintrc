[MASTER]
# Add the src directory to the Python path
init-hook='import sys; import os; sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))'

# Don't warn about specific modules that might be missing
ignored-modules=flutter_signer

[MESSAGES CONTROL]
# Disable specific messages
disable=
    import-error,
    wrong-import-position

[FORMAT]
# Maximum number of characters on a single line
max-line-length=100

[REPORTS]
# Set the output format
output-format=text

[DESIGN]
# Maximum number of arguments for function / method
max-args=10

[SIMILARITIES]
# Minimum lines number of a similarity
min-similarity-lines=5

[TYPECHECK]
# List of members which are set dynamically and missed by pylint inference system
generated-members=REQUEST,acl_users,aq_parent 