# FlutLock Project Intelligence

## Project Patterns

- Python-based command-line tool for automating Flutter app signing
- Focus on security and ease of use for the signing process
- Cross-platform compatibility is essential (Windows, macOS, Linux)
- Starting with a simple approach and evolving to more complex features
- Using custom exception classes to categorize different types of errors
- Supporting customization through configuration files and CLI options
- Implementing features with backward compatibility in mind
- Providing detailed error guidance with troubleshooting steps
- Following test-driven development for new features
- Handling hybrid syntax in build.gradle.kts files by content detection instead of just file extension

## Naming Conventions

- Use clear, descriptive function and variable names
- Follow Python PEP 8 style guidelines
- Use snake_case for variables and functions
- Use CamelCase for classes
- Error classes should have descriptive names ending with "Error"
- Prefix test files with "test*" and test methods with "test*"
- Use descriptive names for CLI parameters with hyphens (e.g., --signing-config-name)

## Security Considerations

- Never hardcode credentials or sensitive information
- Use environment variables or secure input methods for passwords
- Validate all user inputs
- Handle keystores with appropriate file permissions
- Check password strength and provide warnings for weak passwords
- Create backups before modifying critical files
- Use secure defaults when user doesn't provide specific values

## Development Workflow

- Start with a minimal, functional approach
- Use modular functions that can be tested independently
- Document code thoroughly
- Consider cross-platform compatibility in all operations
- Implement comprehensive error handling with specific guidance
- Test new features across multiple platforms before merging
- Follow test-driven development for new features
- Create example scripts to demonstrate new functionality

## Error Handling Patterns

- Use custom exception classes inheriting from a base FlutLockError
- Provide detailed error messages with troubleshooting steps
- Include fallback mechanisms where appropriate
- Structure error handling with try/except blocks for clarity
- Validate input early to catch errors before processing
- Set appropriate file permissions and handle permission errors
- Check for missing dependencies with clear installation guidance
- Include details and suggestions in exception objects
- Categorize errors by type (Keystore, Gradle, Config, etc.)
- Log additional debug information when verbose mode is enabled

## Git Workflow

- Two main branches: `main` (stable) and `development` (ongoing work)
- Feature branches should be created from `development`
- Pull requests should target `development` branch
- Only merge to `main` when code is fully tested and ready for release
- CI workflow runs tests on all branches
- Deployment steps only run on `main` branch
- Tag releases using semantic versioning (e.g., v1.0.0)
- Ensure cross-platform tests pass before merging PRs

## User Preferences

- Clear, informative command-line output
- Guided experience with helpful error messages
- Option for non-interactive mode in CI/CD environments
- Security and reliability are top priorities
- Providing detailed troubleshooting steps for common errors
- Customization options for different environments and workflows
- Environment-specific configurations through variable substitution
- Custom signing configuration names for different build variants

## Code Organization Patterns

- Follow Python package-based architecture
- Place imports in logical order (standard library, then third-party, then local)
- Consolidate similar functionality into single modules
- Integrate feature-related code into core modules rather than creating separate ones
- Ensure backward compatibility for existing users
- Maintain clear entry points with consistent interfaces
- Document the purpose of each module at the top of the file
- Keep entry point scripts minimal, delegating to the package for actual functionality
- Prefer class-based exceptions for better error categorization
- Use command-line argument groups for better organization
- Implement verbose/quiet modes consistently across all modules
- Test functionality across multiple platforms
- Create specific exception classes for different error categories
- Ensure paths work on both Windows and Unix-like systems
- Examine file contents rather than just file extensions to determine syntax style
- Handle multiple configuration variants in build files

## Gradle File Handling Patterns

- Detect file syntax based on content using positive pattern matching for Kotlin features
- Look for specific Kotlin language markers rather than absence of Groovy patterns
- Check for Kotlin-specific syntax like "val", "import kotlin", type declarations, etc.
- Support both pure Kotlin DSL and hybrid Groovy-Kotlin syntax in .kts files
- Account for Flutter's common use of Groovy syntax in .kts files
- Implement robust property loading with try-catch blocks and proper resource management
- Use FileInputStream.use() for automatic resource closing in Kotlin DSL templates
- Add comprehensive null-checking for keystore properties and signing configurations
- Provide informative warning logs for missing configurations
- Support modern Kotlin DSL patterns like getByName() for buildTypes
- Use safer property access with getProperty() instead of direct indexing
- Implement utility functions for safer property loading
- Check for multiple signingConfig lines in release buildType
- Clean up TODO comments when adding signing configurations
- Preserve existing file structure while adding missing configurations
- Create backups before modifying Gradle files
- Handle the null case for storeFile using the ternary operator pattern
- Ensure proper variable scoping and initialization
- Simplify code by combining similar handling paths for different file types
- Improve error messages by including specific file paths in error reporting

## Cross-Platform Considerations

- Use os.path functions for platform-independent path handling
- Normalize paths for cross-platform compatibility
- Test on Windows, macOS, and Linux before releasing
- Use GitHub Actions for automated cross-platform testing
- Handle platform-specific file permissions appropriately
- Account for differences in command execution across platforms
- Provide platform-specific guidance in error messages when relevant
- Consider path separator differences (\\ vs. /) in configurations
- Test with different Python versions on each platform
